
d. Batch Analysis (3.2.S.4.4)
You should include a table with test results for batches (or lots) of the DS
that you have manufactured. For early stage INDs, this may include
toxicology lots, developmental batches, engineering runs, or a single
manufacturing run for clinical grade material. We recommend that you
gain adequate experience with a new clinical manufacturing processes
prior to making clinical material. This is especially critical following
technology transfer to a new manufacturing facility, when manufacturing
changes occur during development, and when multiple manufacturing
facilities will be utilized. Please note that batches manufactured in
different ways should be clearly identified in the submission. Information
regarding process development of these materials should be outlined in the
“Manufacturing Process Development (3.2.S.2.6)” section of the CTD.
We recommend that you annually update this section of your IND as new
batches are produced. You should indicate any batches that fail to meet
release specifications and any action taken to investigate the failure
according to your Quality Unit procedures (for Quality Unit information
36

Contains Nonbinding Recommendations
please see section V.C.1., “Appendices – Facilities and Equipment
(3.2.A.1),” of this guidance). We recommend that you retain samples of
production lots for use in future assay development, validation, or
comparability studies.
e. Justification of Specification (3.2.S.4.5)
You should provide justification for the DS specifications in your IND.
We recognize that acceptance criteria may be adjusted throughout the
product development stages, based on both manufacturing and clinical
experience. For early stage clinical studies, assays used to characterize
production lots may be more variable than those used in later phase
investigations.
For later stage investigational studies in which the primary objective is to
gather meaningful data about product efficacy, we recommend that
acceptance criteria be tightened to ensure batches are well-defined and
consistently manufactured.